rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Genel okuma izni
    match /{document=**} {
      allow read: if true;
    }
    
    // Kullanıcı kuralları
    match /users/{userId} {
      // Oturum açan herkes kullanıcı belgesi oluşturabilir
      allow create: if request.auth != null;
      
      // Kullanıcı kendi belgesini güncelleyebilir
      allow update: if request.auth != null && request.auth.uid == userId;
      
      // Kullanıcı belgesini herkes okuyabilir (gerekirse bu kısıtlanabilir)
      allow read: if true;
      
      // Silme işlemi sadece master rolüne sahip kullanıcılar tarafından yapılabilir
      allow delete: if request.auth != null && 
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'master';
    }
    
    // Master Kate<PERSON><PERSON> kuralları
    match /mastercategories/{categoryId} {
      // Master kategorileri herkes okuyabilir
      allow read: if true;
      
      // Geçici olarak herkesin oluşturmasına, güncellemesine ve silmesine izin ver
      allow create: if 
        (request.resource.data.createdBy == 'system') || 
        (request.auth != null && 
          exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
          get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'master');
      
      allow update: if 
        (request.resource.data.updatedBy == 'system') || 
        (request.auth != null && 
          exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
          get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'master');
      
      allow delete: if 
        (resource.data.pendingDelete == true) ||
        (request.auth != null && 
          exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
          get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'master');
    }
    
    // Sipariş kuralları
    match /orders/{orderId} {
      // Sipariş okuma kuralları
      allow read: if request.auth != null && (
        // Kullanıcı kendi siparişlerini okuyabilir
        resource.data.userId == request.auth.uid ||
        // Master veya ilgili firmalar siparişleri okuyabilir
        (exists(/databases/$(database)/documents/users/$(request.auth.uid)) && (
          get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'master' ||
          (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'firm' &&
           resource.data.items != null && resource.data.items.size() > 0 &&
           resource.data.items[0].restaurantId == get(/databases/$(database)/documents/users/$(request.auth.uid)).data.firmId)
        ))
      );

      // Sipariş oluşturma kuralları - kullanıcılar kendi siparişlerini oluşturabilir
      allow create: if request.auth != null &&
        (request.resource.data.userId == request.auth.uid ||
         request.resource.data.userId == 'guest');

      // Sipariş güncelleme kuralları - sadece firmalar ve master kullanıcılar için
      allow update: if request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) && (
          get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'master' ||
          (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'firm' &&
           resource.data.items != null && resource.data.items.size() > 0 &&
           resource.data.items[0].restaurantId == get(/databases/$(database)/documents/users/$(request.auth.uid)).data.firmId)
        );

      // Sipariş silme kuralları - sadece master rolü için
      allow delete: if request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'master';
    }
    
    // Firma kuralları
    match /firms/{firmId} {
      // Firma belgelerini herkes okuyabilir
      allow read: if true;
      
      // Yeni firma belgesi oluşturma - oturum açan herkes yapabilir (masterfasty uygulamasında)
      allow create: if request.auth != null;
      
      // Güncelleme kuralları - firma sahibi veya master rolü
      allow update: if request.auth != null && (
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) && (
          get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'master' ||
          (exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
           get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'firm' &&
           get(/databases/$(database)/documents/users/$(request.auth.uid)).data.firmId == firmId)
        )
      );
      
      // Silme işlemi sadece master rolüne sahip kullanıcılar tarafından yapılabilir
      allow delete: if request.auth != null && 
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'master';
    }
    
    // Rezervasyon kuralları
    match /reservations/{reservationId} {
      // Rezervasyonları görüntüleme kuralları
      allow read: if request.auth != null && (
        resource.data.userId == request.auth.uid ||
        (exists(/databases/$(database)/documents/users/$(request.auth.uid)) && (
          get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'master' ||
          (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'firm' &&
           get(/databases/$(database)/documents/users/$(request.auth.uid)).data.firmId == resource.data.firmId)
        ))
      );
      
      // Rezervasyon oluşturma kuralları - kullanıcılar sadece kendi adlarına rezervasyon yapabilir
      allow create: if request.auth != null && 
        request.resource.data.userId == request.auth.uid && 
        request.resource.data.status == 'pending';
      
      // Rezervasyon güncelleme kuralları
      allow update: if request.auth != null && (
        (resource.data.userId == request.auth.uid && request.resource.data.status == 'cancelled') ||
        (exists(/databases/$(database)/documents/users/$(request.auth.uid)) && (
          get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'master' ||
          (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'firm' &&
           get(/databases/$(database)/documents/users/$(request.auth.uid)).data.firmId == resource.data.firmId)
        ))
      );
      
      // Rezervasyonlar silinemez
      allow delete: if false;
    }
    
    // Kategori kuralları
    match /categories/{categoryId} {
      // Kategorileri herkes okuyabilir
      allow read: if true;
      
      // Oluşturma, güncelleme ve silme kuralları
      allow create, update, delete: if request.auth != null && (
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) && (
          get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'master' ||
          (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'firm' &&
           (resource.data != null ? 
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.firmId == resource.data.firmId :
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.firmId == request.resource.data.firmId)
          )
        )
      );
    }
    
    // Menü ürünleri kuralları
    match /menuItems/{itemId} {
      // Menü öğelerini herkes okuyabilir
      allow read: if true;
      
      // Oluşturma kuralları
      allow create: if request.auth != null && 
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) && (
          get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'master' ||
          (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'firm' &&
           get(/databases/$(database)/documents/users/$(request.auth.uid)).data.firmId == request.resource.data.firmId)
        );
      
      // Güncelleme ve silme kuralları
      allow update, delete: if request.auth != null && 
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) && (
          get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'master' ||
          (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'firm' &&
           get(/databases/$(database)/documents/users/$(request.auth.uid)).data.firmId == resource.data.firmId)
        );
    }
    
    // Kampanya kuralları
    match /campaigns/{campaignId} {
      // Kampanyaları herkes okuyabilir
      allow read: if true;
      
      // Oluşturma kuralları
      allow create: if request.auth != null && 
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) && (
          get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'master' ||
          (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'firm' &&
           get(/databases/$(database)/documents/users/$(request.auth.uid)).data.firmId == request.resource.data.firmId)
        );
      
      // Güncelleme ve silme kuralları
      allow update, delete: if request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) && (
          get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'master' ||
          (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'firm' &&
           get(/databases/$(database)/documents/users/$(request.auth.uid)).data.firmId == resource.data.firmId)
        );
    }

    // Master İndirim kuralları
    match /discounts/{discountId} {
      // İndirimleri okuma kuralları
      allow read: if true;

      // İndirim oluşturma kuralları - hem API'dan gelen ('system' olarak işaretlenmiş) hem de master kullanıcıların oluşturmasına izin ver
      allow create: if
        (request.resource.data.createdBy == 'system') ||
        (request.auth != null &&
          exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
          get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'master');

      // İndirim güncelleme ve silme kuralları - hem API'dan gelen hem de master kullanıcıların güncellemesine izin ver
      allow update, delete: if
        (request.resource.data.updatedBy == 'system') ||
        (request.auth != null &&
          exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
          get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'master');
    }

    // Galeri kuralları
    match /gallery/{imageId} {
      // Galeri görsellerini herkes okuyabilir
      allow read: if true;

      // Oluşturma, güncelleme ve silme kuralları
      allow create, update, delete: if request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) && (
          get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'master' ||
          (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'firm' &&
           get(/databases/$(database)/documents/users/$(request.auth.uid)).data.firmId == (
             resource.data != null ? resource.data.firmId : request.resource.data.firmId)
          )
        );
    }

    // Değerlendirme (Ratings) kuralları
    match /ratings/{ratingId} {
      // Değerlendirmeleri herkes okuyabilir
      allow read: if true;

      // Değerlendirme oluşturma kuralları - kullanıcılar sadece kendi adlarına değerlendirme yapabilir
      allow create: if request.auth != null &&
        request.resource.data.userId == request.auth.uid;

      // Değerlendirme güncelleme kuralları - kullanıcılar sadece kendi değerlendirmelerini güncelleyebilir
      allow update: if request.auth != null &&
        resource.data.userId == request.auth.uid;

      // Değerlendirme silme kuralları - kullanıcılar kendi değerlendirmelerini veya master rolü silebilir
      allow delete: if request.auth != null && (
        resource.data.userId == request.auth.uid ||
        (exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'master')
      );
    }
  }
}
