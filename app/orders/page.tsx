'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/lib/AuthContext';
import { Order, OrderStatus, getRestaurantOrders, updateOrderStatus, orderStatusLabels, getUserDetails, UserDetails } from '@/lib/orderService';
import { ShoppingBag, Clock, CheckCircle, XCircle, Package, ChevronDown, ChevronUp, RefreshCw, AlertCircle, User, Mail, Phone } from 'lucide-react';
import toast from 'react-hot-toast';

export default function OrdersPage() {
  const { firmData, loading: authLoading } = useAuth();
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedOrder, setExpandedOrder] = useState<string | null>(null);
  const [statusUpdating, setStatusUpdating] = useState<Record<string, boolean>>({});
  const [userDetails, setUserDetails] = useState<Record<string, UserDetails | null>>({});

  // Siparişleri yükle
  useEffect(() => {
    if (authLoading) return;
    
    if (!firmData?.id) {
      setLoading(false);
      setError('Firma bilgilerine erişilemiyor.');
      return;
    }
    
    loadOrders();
  }, [firmData, authLoading]);

  const loadOrders = async () => {
    try {
      setLoading(true);
      setError(null);
      
      if (!firmData?.id) {
        setError('Firma bilgilerine erişilemiyor.');
        setLoading(false);
        return;
      }
      
      const data = await getRestaurantOrders(firmData.id);
      setOrders(data);
    } catch (err) {
      console.error('Siparişler yüklenirken hata:', err);
      setError('Siparişler yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  // Sipariş detaylarını aç/kapat
  const toggleOrderDetails = async (orderId: string) => {
    // Eğer zaten açıksa kapat
    if (expandedOrder === orderId) {
      setExpandedOrder(null);
      return;
    }
    
    // Açılacaksa ve kullanıcı bilgileri yüklenmemişse, yükle
    const order = orders.find(o => o.id === orderId);
    if (order && !userDetails[order.userId] && order.userId) {
      try {
        const userData = await getUserDetails(order.userId);
        setUserDetails(prev => ({ ...prev, [order.userId]: userData }));
      } catch (err) {
        console.error('Kullanıcı bilgileri yüklenirken hata:', err);
      }
    }
    
    setExpandedOrder(orderId);
  };

  // Sipariş statüsünü güncelle
  const handleStatusUpdate = async (orderId: string, status: OrderStatus) => {
    try {
      setStatusUpdating(prev => ({ ...prev, [orderId]: true }));

      console.log('Sipariş güncelleme işlemi başlatılıyor:', {
        orderId,
        status,
        firmId: firmData?.id,
        firmRole: firmData?.role
      });

      const success = await updateOrderStatus(orderId, status);

      if (success) {
        // Sipariş listesini güncelle
        setOrders(orders.map(order =>
          order.id === orderId ? { ...order, status, updatedAt: new Date().toISOString() } : order
        ));

        toast.success(`Sipariş durumu "${orderStatusLabels[status]}" olarak güncellendi.`);
      } else {
        toast.error('Sipariş durumu güncellenirken bir hata oluştu. Lütfen konsolu kontrol edin.');
      }
    } catch (err) {
      console.error('Sipariş durumu güncellenirken hata:', err);
      toast.error(`Sipariş durumu güncellenirken bir hata oluştu: ${err instanceof Error ? err.message : 'Bilinmeyen hata'}`);
    } finally {
      setStatusUpdating(prev => ({ ...prev, [orderId]: false }));
    }
  };

  // Statüs rengini ve ikonunu belirle
  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'hazirlaniyor':
        return { color: 'bg-yellow-100 text-yellow-800', icon: <Clock className="h-4 w-4 mr-1" /> };
      case 'onaylandi':
        return { color: 'bg-green-100 text-green-800', icon: <CheckCircle className="h-4 w-4 mr-1" /> };
      case 'iptaledildi':
        return { color: 'bg-red-100 text-red-800', icon: <XCircle className="h-4 w-4 mr-1" /> };
      case 'teslimedildi':
        return { color: 'bg-blue-100 text-blue-800', icon: <Package className="h-4 w-4 mr-1" /> };
      default:
        return { color: 'bg-gray-100 text-gray-800', icon: <Clock className="h-4 w-4 mr-1" /> };
    }
  };

  // Tarih formatını düzenleme
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('tr-TR', { 
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading || authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="p-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Siparişler</h1>
          <p className="text-gray-500 mt-1">Restoranınıza gelen siparişleri yönetin</p>
        </div>
        
        <button 
          onClick={loadOrders}
          className="flex items-center bg-primary text-white px-4 py-2 rounded-md hover:bg-primary/90 transition-colors"
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Yenile
        </button>
      </div>
      
      {error && (
        <div className="bg-red-50 text-red-600 p-4 rounded-lg mb-6 flex items-center">
          <AlertCircle className="h-5 w-5 mr-2" />
          {error}
        </div>
      )}
      
      {orders.length === 0 ? (
        <div className="bg-white rounded-lg shadow-sm border p-12 text-center">
          <ShoppingBag className="h-16 w-16 mx-auto text-gray-300 mb-4" />
          <h3 className="text-xl font-semibold text-gray-700 mb-2">Henüz sipariş bulunmuyor</h3>
          <p className="text-gray-500">Siparişler geldiğinde burada görüntülenecektir.</p>
        </div>
      ) : (
        <div className="space-y-4">
          {orders.map((order) => (
            <div key={order.id} className="bg-white rounded-lg shadow-sm border overflow-hidden">
              {/* Sipariş başlığı */}
              <div 
                className="p-4 flex flex-col md:flex-row md:items-center justify-between border-b cursor-pointer hover:bg-gray-50"
                onClick={() => toggleOrderDetails(order.id)}
              >
                <div className="flex items-center">
                  <ShoppingBag className="h-5 w-5 text-gray-500 mr-3" />
                  <div>
                    <div className="font-medium">{`Sipariş #${order.id.substr(0, 8)}`}</div>
                    <div className="text-sm text-gray-500">
                      {formatDate(order.createdAt)}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center mt-4 md:mt-0 space-x-4">
                  <div className="text-lg font-semibold">
                    {order.totalAmount} ₺
                  </div>
                  
                  <div className={`px-3 py-1 rounded-full text-xs font-medium flex items-center ${getStatusInfo(order.status).color}`}>
                    {getStatusInfo(order.status).icon}
                    {orderStatusLabels[order.status as OrderStatus] || 'Hazırlanıyor'}
                  </div>
                  
                  {expandedOrder === order.id ? (
                    <ChevronUp className="h-5 w-5 text-gray-400" />
                  ) : (
                    <ChevronDown className="h-5 w-5 text-gray-400" />
                  )}
                </div>
              </div>
              
              {/* Sipariş detayları */}
              {expandedOrder === order.id && (
                <div className="p-4 bg-gray-50">
                  {/* Sipariş ürünleri */}
                  <div className="mb-6">
                    <h3 className="font-semibold text-gray-700 mb-2">Sipariş Ürünleri</h3>
                    <div className="space-y-2">
                      {order.items
                        .filter(item => item.restaurantId === firmData?.id) // Sadece bu restorana ait ürünleri göster
                        .map((item, index) => (
                          <div key={index} className="flex justify-between items-center p-2 bg-white rounded-md">
                            <div className="flex items-center space-x-3">
                              <div className="w-12 h-12 rounded-md overflow-hidden bg-gray-200">
                                {item.imageUrl && (
                                  <img
                                    src={item.imageUrl}
                                    alt={item.name}
                                    className="w-full h-full object-cover"
                                  />
                                )}
                              </div>
                              <div>
                                <div className="font-medium">{item.name}</div>
                                <div className="text-sm text-gray-500">
                                  {`${item.quantity} adet × ${item.price} ₺`}
                                </div>
                              </div>
                            </div>
                            <div className="font-semibold">
                              {item.quantity * item.price} ₺
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>
                  
                  {/* Sipariş bilgileri */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div>
                      <h3 className="font-semibold text-gray-700 mb-2">Sipariş Bilgileri</h3>
                      <div className="bg-white p-3 rounded-md space-y-2">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Sipariş ID:</span>
                          <span className="font-medium">{order.id}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Ödeme Yöntemi:</span>
                          <span className="font-medium">{order.paymentMethod === 'credit' ? 'Kredi Kartı' : 'Nakit'}</span>
                        </div>
                      </div>
                    </div>
                    
                    {/* Müşteri bilgileri */}
                    <div>
                      <h3 className="font-semibold text-gray-700 mb-2">Müşteri Bilgileri</h3>
                      <div className="bg-white p-3 rounded-md">
                        {userDetails[order.userId] ? (
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <div className="min-w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                                {userDetails[order.userId]?.photoURL ? (
                                  <img 
                                    src={userDetails[order.userId]?.photoURL} 
                                    alt={userDetails[order.userId]?.displayName}
                                    className="w-full h-full object-cover" 
                                  />
                                ) : (
                                  <User className="h-4 w-4 text-gray-500" />
                                )}
                              </div>
                              <div className="font-medium">{userDetails[order.userId]?.displayName || 'İsimsiz Kullanıcı'}</div>
                            </div>
                            
                            <div className="flex items-center text-sm text-gray-600 gap-2">
                              <Mail className="h-4 w-4" />
                              <span>{userDetails[order.userId]?.email || 'E-posta bilgisi yok'}</span>
                            </div>
                            
                            {userDetails[order.userId]?.phoneNumber && (
                              <div className="flex items-center text-sm text-gray-600 gap-2">
                                <Phone className="h-4 w-4" />
                                <span>{userDetails[order.userId]?.phoneNumber}</span>
                              </div>
                            )}
                          </div>
                        ) : order.userId ? (
                          <div className="flex items-center justify-center py-3">
                            <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-primary"></div>
                          </div>
                        ) : (
                          <div className="text-center py-2 text-gray-500">Misafir Sipariş</div>
                        )}
                      </div>
                    </div>
                    
                    {/* Sipariş durumu güncelleme */}
                    <div className="md:col-span-2">
                      <h3 className="font-semibold text-gray-700 mb-2">Sipariş Durumunu Güncelle</h3>
                      <div className="bg-white p-3 rounded-md space-y-3">
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                          <button
                            onClick={() => handleStatusUpdate(order.id, 'hazirlaniyor')}
                            disabled={order.status === 'hazirlaniyor' || statusUpdating[order.id]}
                            className={`p-2 rounded-md text-center flex items-center justify-center ${
                              order.status === 'hazirlaniyor' 
                                ? 'bg-yellow-100 text-yellow-800 border border-yellow-300' 
                                : 'bg-gray-100 hover:bg-yellow-50 hover:text-yellow-800 transition-colors'
                            }`}
                          >
                            <Clock className="h-4 w-4 mr-1" />
                            <span>Hazırlanıyor</span>
                          </button>
                          
                          <button
                            onClick={() => handleStatusUpdate(order.id, 'onaylandi')}
                            disabled={order.status === 'onaylandi' || statusUpdating[order.id]}
                            className={`p-2 rounded-md text-center flex items-center justify-center ${
                              order.status === 'onaylandi' 
                                ? 'bg-green-100 text-green-800 border border-green-300' 
                                : 'bg-gray-100 hover:bg-green-50 hover:text-green-800 transition-colors'
                            }`}
                          >
                            <CheckCircle className="h-4 w-4 mr-1" />
                            <span>Onaylandı</span>
                          </button>
                          
                          <button
                            onClick={() => handleStatusUpdate(order.id, 'teslimedildi')}
                            disabled={order.status === 'teslimedildi' || statusUpdating[order.id]}
                            className={`p-2 rounded-md text-center flex items-center justify-center ${
                              order.status === 'teslimedildi' 
                                ? 'bg-blue-100 text-blue-800 border border-blue-300' 
                                : 'bg-gray-100 hover:bg-blue-50 hover:text-blue-800 transition-colors'
                            }`}
                          >
                            <Package className="h-4 w-4 mr-1" />
                            <span>Teslim Edildi</span>
                          </button>
                          
                          <button
                            onClick={() => handleStatusUpdate(order.id, 'iptaledildi')}
                            disabled={order.status === 'iptaledildi' || statusUpdating[order.id]}
                            className={`p-2 rounded-md text-center flex items-center justify-center ${
                              order.status === 'iptaledildi' 
                                ? 'bg-red-100 text-red-800 border border-red-300' 
                                : 'bg-gray-100 hover:bg-red-50 hover:text-red-800 transition-colors'
                            }`}
                          >
                            <XCircle className="h-4 w-4 mr-1" />
                            <span>İptal Edildi</span>
                          </button>
                        </div>
                        
                        {statusUpdating[order.id] && (
                          <div className="flex justify-center">
                            <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-primary"></div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
} 