{"name": "firmafasty", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/themes": "^3.1.4", "@react-google-maps/api": "^2.20.6", "date-fns": "^4.1.0", "firebase": "^10.13.2", "lucide-react": "^0.456.0", "next": "14.2.13", "react": "^18", "react-dom": "^18", "react-hot-toast": "^2.5.2", "react-icons": "^5.3.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.13", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}