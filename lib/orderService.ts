import { db } from './firebase';
import { collection, getDocs, query, where, orderBy, doc, updateDoc, getDoc } from 'firebase/firestore';

export interface OrderItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  imageUrl: string;
  restaurantId: string;
  restaurantName: string;
}

export interface Order {
  id: string;
  userId: string;
  items: OrderItem[];
  status: string;
  totalAmount: number;
  createdAt: string;
  updatedAt: string;
  paymentMethod: string;
}

// Kullanıcı bilgileri arayüzü
export interface UserDetails {
  id: string;
  displayName: string;
  email: string;
  phoneNumber?: string;
  photoURL?: string;
}

// Statüs türleri
export type OrderStatus = 'hazirlaniyor' | 'onaylandi' | 'iptaledildi' | 'teslimedildi';

// Statüs Türkçe karşılıkları
export const orderStatusLabels: Record<OrderStatus, string> = {
  hazirlaniyor: 'Hazırlanıyor',
  onaylandi: 'Onaylandı',
  iptaledildi: '<PERSON>pta<PERSON> Edildi',
  teslimedildi: 'Teslim Edildi'
};

// Kullanıcı bilgilerini getiren fonksiyon
export const getUserDetails = async (userId: string): Promise<UserDetails | null> => {
  try {
    if (!userId) return null;
    
    const userDoc = await getDoc(doc(db, 'users', userId));
    
    if (!userDoc.exists()) {
      return null;
    }
    
    const userData = userDoc.data();
    return {
      id: userDoc.id,
      displayName: userData.displayName || userData.name || 'İsimsiz Kullanıcı',
      email: userData.email || '',
      phoneNumber: userData.phoneNumber || '',
      photoURL: userData.photoURL || userData.profileImageUrl || ''
    } as UserDetails;
  } catch (error) {
    console.error("Kullanıcı bilgileri getirme hatası:", error);
    return null;
  }
};

// Firmaya ait siparişleri getiren fonksiyon
export const getRestaurantOrders = async (restaurantId: string): Promise<Order[]> => {
  try {
    // orders collection'ında restaurantId ile eşleşen siparişleri bul
    const q = query(
      collection(db, 'orders'),
      orderBy('createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    const orders: Order[] = [];
    
    querySnapshot.forEach((doc) => {
      const orderData = doc.data();
      
      // İlk ürünün restaurantId'si verilen restaurantId ile eşleşiyorsa
      // Burada tüm siparişleri kontrol edip en az bir ürünü bizim firmaya ait olan siparişleri alıyoruz
      const hasItemsFromRestaurant = orderData.items && orderData.items.some(
        (item: OrderItem) => item.restaurantId === restaurantId
      );
      
      if (hasItemsFromRestaurant) {
        orders.push({
          id: doc.id,
          ...orderData,
          items: orderData.items || [],
          status: orderData.status || 'hazirlaniyor',
          createdAt: orderData.createdAt?.toDate?.().toISOString() || new Date().toISOString(),
          updatedAt: orderData.updatedAt?.toDate?.().toISOString() || new Date().toISOString()
        } as Order);
      }
    });
    
    return orders;
  } catch (error) {
    console.error("Siparişleri getirme hatası:", error);
    return [];
  }
};

// Sipariş detaylarını getiren fonksiyon
export const getOrderDetails = async (orderId: string): Promise<Order | null> => {
  try {
    const orderDoc = await getDoc(doc(db, 'orders', orderId));
    
    if (!orderDoc.exists()) {
      return null;
    }
    
    const orderData = orderDoc.data();
    return {
      id: orderDoc.id,
      ...orderData,
      items: orderData.items || [],
      status: orderData.status || 'hazirlaniyor',
      createdAt: orderData.createdAt?.toDate?.().toISOString() || new Date().toISOString(),
      updatedAt: orderData.updatedAt?.toDate?.().toISOString() || new Date().toISOString()
    } as Order;
    
  } catch (error) {
    console.error("Sipariş detaylarını getirme hatası:", error);
    return null;
  }
};

// Sipariş statüsünü güncelleyen fonksiyon
export const updateOrderStatus = async (orderId: string, status: OrderStatus): Promise<boolean> => {
  try {
    console.log('Sipariş güncelleme başlatılıyor:', { orderId, status });

    await updateDoc(doc(db, 'orders', orderId), {
      status,
      updatedAt: new Date()
    });

    console.log('Sipariş başarıyla güncellendi:', { orderId, status });
    return true;
  } catch (error) {
    console.error("Sipariş statüsü güncelleme hatası:", error);

    // Hata detaylarını logla
    if (error instanceof Error) {
      console.error("Hata mesajı:", error.message);
      console.error("Hata kodu:", (error as any).code);
    }

    return false;
  }
};