'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { onAuthStateChanged, User } from 'firebase/auth';
import { auth, db } from './firebase';
import { doc, getDoc } from 'firebase/firestore';
import { useRouter, usePathname } from 'next/navigation';

interface FirmData {
  id: string;
  name: string;
  email: string;
  logo?: string;
  isActive: boolean;
  firmId: string;
  role: string;
  gallery?: string[];
  city?: string;
  district?: string;
  minReservationAmount?: string;
}

interface AuthContextType {
  user: User | null;
  firmData: FirmData | null;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  firmData: null,
  loading: true,
});

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [firmData, setFirmData] = useState<FirmData | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setUser(user);

      if (user) {
        // Önce users koleksiyonundan kullanıcı bilgilerini al
        try {
          const userDoc = await getDoc(doc(db, 'users', user.uid));
          if (userDoc.exists() && userDoc.data().role === 'firm') {
            const userData = userDoc.data();

            // Firma bilgilerini getir
            const firmDoc = await getDoc(doc(db, 'firms', userData.firmId));
            if (firmDoc.exists()) {
              const firmData = firmDoc.data();
              const firmInfo = {
                id: firmDoc.id,
                name: firmData.name,
                email: userData.email,
                logo: firmData.logo,
                isActive: true,
                firmId: userData.firmId,
                role: userData.role,
                gallery: firmData.gallery,
                city: firmData.city,
                district: firmData.district,
                minReservationAmount: firmData.minReservationAmount
              };

              console.log('Firma bilgileri yüklendi:', {
                userId: user.uid,
                firmId: userData.firmId,
                role: userData.role,
                firmName: firmData.name
              });

              setFirmData(firmInfo);
              // Firma bilgileri başarıyla yüklendikten sonra loading'i kapat
              setLoading(false);
            } else {
              // Firma bulunamadıysa loading'i kapat ve kullanıcıyı yönlendir
              setLoading(false);
              setFirmData(null);
              if (!pathname.startsWith('/auth')) {
                router.push('/auth/login');
              }
            }
          } else {
            // Kullanıcı firma değilse veya bulunamadıysa çıkış yap
            await auth.signOut();
            setFirmData(null);
            setLoading(false);
            if (!pathname.startsWith('/auth')) {
              router.push('/auth/login');
            }
          }
        } catch (error) {
          console.error('Firma bilgileri alınamadı:', error);
          setFirmData(null);
          setLoading(false);
          if (!pathname.startsWith('/auth')) {
            router.push('/auth/login');
          }
        }
      } else {
        setFirmData(null);
        setLoading(false);
        if (!pathname.startsWith('/auth')) {
          router.push('/auth/login');
        }
      }
    });

    return () => unsubscribe();
  }, [pathname, router]);

  // Auth olmayan kullanıcıları login sayfasına yönlendir
  useEffect(() => {
    if (!loading && !user && !pathname.startsWith('/auth')) {
      router.push('/auth/login');
    }
  }, [loading, user, pathname, router]);

  return (
    <AuthContext.Provider value={{ user, firmData, loading }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);