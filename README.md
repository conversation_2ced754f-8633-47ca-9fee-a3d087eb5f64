# FirmaFasty - <PERSON>rma Yönetim Paneli

Bu proje, işletme sahiplerinin firmalarını yönetebilecekleri kapsamlı bir panel uygulamasıdır. Next.js ve Firebase kullanılarak geliştirilmiştir.

## Teknolojiler

- **Next.js 14** - React tabanlı full-stack framework, App Router yapısı
- **Firebase**
    - **Authentication** - Güvenli kullanıcı girişi ve yönetimi
    - **Firestore** - NoSQL veritabanı
    - **Storage** - Resim ve dosya depolama
    - **Cloud Functions** - Sunucu taraflı işlemler
- **TypeScript** - Statik tip kontrolü ve geliştirici deneyimi
- **Tailwind CSS** - Utility-first CSS framework
- **React Hook Form** - Form yönetimi ve doğrulama
- **Zod** - Şema doğrulama
- **React Google Maps API** - Konum seçimi ve harita entegrasyonu
- **Lucide React** - Vektör ikon kütüphanesi
- **React Hot Toast** - Kullanıcı bildirimleri
- **SWR** - Veri getirme ve önbelleğe alma
- **date-fns** - Tarih işlemleri

## Özellikler

### Firma Profil Yönetimi
- Firma bilgilerini düzenleme (isim, açıklama, logo, iletişim bilgileri)
- Çalışma saatlerini ayarlama
- Konum ve adres bilgilerini güncelleme
- Sosyal medya hesaplarını yönetme

### Menü Yönetimi
- Kategori oluşturma ve düzenleme
- Ürün ekleme, düzenleme ve silme
- Ürün görselleri yükleme
- Fiyat ve stok durumu güncelleme
- Özel ürün seçenekleri tanımlama

### Rezervasyon Sistemi
- Gelen rezervasyonları görüntüleme
- Rezervasyon onaylama, reddetme veya iptal etme
- Rezervasyon detaylarını inceleme
- Rezervasyon geçmişini görüntüleme
- Müşteri notlarını görme

### Kampanya Yönetimi
- Özel kampanyalar oluşturma
- İndirim kodları tanımlama
- Kampanya süresi belirleme
- Hedef kitle seçimi
- Kampanya performansını izleme

### Galeri Yönetimi
- Firma fotoğraflarını yükleme ve düzenleme
- Galeri kategorileri oluşturma
- Görselleri sıralama ve düzenleme

### Analitik ve Raporlama
- Ziyaretçi istatistiklerini görüntüleme
- Rezervasyon analizleri
- Popüler ürünleri tespit etme
- Gelir raporları

## Kurulum ve Geliştirme

### Gereksinimler
- Node.js 18.0 veya üzeri
- npm, yarn, pnpm veya bun

### Kurulum

```bash
# Depoyu klonlayın
git clone https://github.com/kullanici/firmafasty.git
cd firmafasty

# Bağımlılıkları yükleyin
npm install
# veya
yarn install

# .env.local dosyasını oluşturun
cp .env.example .env.local
# Firebase yapılandırma bilgilerinizi .env.local dosyasına ekleyin
```

### Geliştirme Sunucusu

```bash
npm run dev
# veya
yarn dev
# veya
pnpm dev
# veya
bun dev
```

[http://localhost:3000](http://localhost:3000) adresini tarayıcınızda açarak sonucu görebilirsiniz.

### Derleme

```bash
npm run build
# veya
yarn build
```

## Proje Yapısı

```
firmafasty/
├── app/                  # Next.js App Router
│   ├── api/              # API rotaları
│   ├── auth/             # Kimlik doğrulama sayfaları
│   ├── dashboard/        # Panel sayfaları
│   └── ...
├── components/           # Yeniden kullanılabilir bileşenler
│   ├── ui/               # UI bileşenleri
│   ├── forms/            # Form bileşenleri
│   └── ...
├── lib/                  # Yardımcı fonksiyonlar ve kütüphaneler
│   ├── firebase/         # Firebase yapılandırması
│   ├── hooks/            # Özel React hook'ları
│   └── utils/            # Yardımcı fonksiyonlar
├── public/               # Statik dosyalar
└── ...
```

## Dağıtım

Bu Next.js uygulamasını dağıtmanın en kolay yolu, Next.js'in yaratıcıları olan Vercel Platform'u kullanmaktır.

Daha fazla bilgi için [Next.js dağıtım belgeleri](https://nextjs.org/docs/app/building-your-application/deploying)ne göz atın.

## Lisans

Bu proje [MIT lisansı](LICENSE) altında lisanslanmıştır.